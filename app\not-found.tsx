import Link from "next/link"
import { But<PERSON> } from "@/components/ui/button"
import { Home, Search } from "lucide-react"

export default function NotFound() {
  return (
    <div className="container mx-auto px-6 py-32 text-center">
      <h1 className="text-6xl font-bold text-gray-900 mb-4">404</h1>
      <h2 className="text-3xl font-semibold text-gray-800 mb-4">
        Sidan kunde inte hittas
      </h2>
      <p className="text-xl text-gray-600 mb-8 max-w-2xl mx-auto">
        Oj! Det verkar som att sidan du letar efter inte finns. Den kan ha flyttats eller tagits bort.
      </p>
      
      <div className="flex gap-4 justify-center">
        <Link href="/">
          <Button size="lg" className="gap-2">
            <Home className="w-4 h-4" />
            Till startsidan
          </Button>
        </Link>
        <Link href="/guider">
          <Button size="lg" variant="outline" className="gap-2">
            <Search className="w-4 h-4" />
            Bläddra guider
          </Button>
        </Link>
      </div>
      
      <div className="mt-16 p-8 bg-green-50 rounded-lg max-w-2xl mx-auto">
        <h3 className="text-xl font-semibold mb-4">Populära kategorier</h3>
        <div className="flex flex-wrap gap-2 justify-center">
          <Link href="/kategorier/hem-tradgard">
            <Button variant="ghost" size="sm">Hem & Trädgård</Button>
          </Link>
          <Link href="/kategorier/teknik">
            <Button variant="ghost" size="sm">Teknik</Button>
          </Link>
          <Link href="/kategorier/mat-dryck">
            <Button variant="ghost" size="sm">Mat & Dryck</Button>
          </Link>
          <Link href="/kategorier/halsa-traaning">
            <Button variant="ghost" size="sm">Hälsa & Träning</Button>
          </Link>
        </div>
      </div>
    </div>
  )
}