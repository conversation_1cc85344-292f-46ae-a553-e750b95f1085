import type { <PERSON>ada<PERSON> } from "next"
import Link from "next/link"
import { getGuides, getCategories } from "@/lib/guides-data"
import GuideCard from "@/components/guide-card"
import { Button } from "@/components/ui/button"

export const metadata: Metadata = {
  title: "Kategorier - Utforska guider efter ämne",
  description:
    "Bläddra bland våra guider efter kategori för att hitta exakt den information du söker inom olika ämnesområden.",
  keywords: ["kategorier", "ämnen", "guider", "steg-för-steg", "instruktioner", "tips"],
}

export default async function CategoriesPage() {
  // Fetch all guides and categories
  const [guides, categoryNames] = await Promise.all([
    getGuides(),
    getCategories()
  ])

  // Count guides in each category
  const categories = guides.reduce<Record<string, number>>((acc, guide) => {
    const category = guide.category
    acc[category] = (acc[category] || 0) + 1
    return acc
  }, {})

  // Group guides by category
  const guidesByCategory = guides.reduce<Record<string, typeof guides>>((acc, guide) => {
    const category = guide.category
    if (!acc[category]) {
      acc[category] = []
    }
    acc[category].push(guide)
    return acc
  }, {})

  return (
    <div className="container mx-auto px-4 md:px-6 py-12">
      <div className="max-w-3xl mx-auto text-center mb-12">
        <h1 className="text-4xl font-bold tracking-tight mb-4 font-serif">Kategorier</h1>
        <p className="text-xl text-muted-foreground">
          Utforska våra guider efter kategori för att hitta exakt den information du söker.
        </p>
      </div>

      {/* Category list */}
      <div className="flex flex-wrap justify-center gap-3 mb-16">
        {Object.entries(categories).map(([category, count]) => (
          <Button
            key={category}
            variant="outline"
            className="rounded-full px-6 py-2 border-primary/20 hover:bg-primary/10"
            asChild
          >
            <Link href={`#${category.toLowerCase().replace(/\s+/g, '-')}`}>
              {category} <span className="ml-2 text-xs bg-primary/10 px-2 py-0.5 rounded-full">{count}</span>
            </Link>
          </Button>
        ))}
      </div>

      {/* Categories with guides */}
      <div className="space-y-16">
        {Object.entries(guidesByCategory).map(([category, categoryGuides]) => (
          <section key={category} id={category.toLowerCase().replace(/\s+/g, '-')}>
            <div className="mb-8">
              <div className="inline-block rounded-full bg-primary/10 px-3 py-1 text-xs font-medium text-primary mb-3">
                Kategori
              </div>
              <h2 className="text-3xl font-bold tracking-tight mb-4 font-serif">{category}</h2>
              <p className="text-muted-foreground">
                Utforska våra {categoryGuides.length} guider inom {category.toLowerCase()}.
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {categoryGuides.map((guide) => (
                <GuideCard key={guide.slug} guide={guide} />
              ))}
            </div>
          </section>
        ))}
      </div>
    </div>
  )
}
