import type { <PERSON>ada<PERSON> } from "next"
import { Mail } from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"

export const metadata: Metadata = {
  title: "Kontakt - Guidebanken",
  description:
    "Kontakta oss på Guidebanken för frågor eller samarbeten. Vi hjälper dig gärna med information om våra guider.",
  keywords: ["kontakt", "guidebanken", "samarbete", "e-post", "frågor"],
}

export default function ContactPage() {
  return (
    <div className="container mx-auto px-4 md:px-6 py-12">
      <div className="max-w-3xl mx-auto">
        <h1 className="text-4xl font-bold tracking-tight mb-4">Kontakta oss</h1>
        <p className="text-xl text-muted-foreground mb-12">
          Har du frågor eller är intresserad av att samarbeta med oss? Vi hör gärna från dig!
        </p>

        <div className="space-y-12">
          {/* Kontaktinformation */}
          <div className="bg-muted p-8 rounded-lg">
            <div className="flex items-start gap-4 mb-6">
              <Mail className="h-6 w-6 text-primary mt-1" />
              <div>
                <h2 className="text-2xl font-bold mb-2">Kontakta oss</h2>
                <p className="text-muted-foreground mb-4">
                  För allmänna frågor, feedback eller förfrågningar, vänligen kontakta oss via e-post:
                </p>
                <a href="mailto:<EMAIL>" className="text-primary hover:underline text-lg font-medium">
                  <EMAIL>
                </a>
              </div>
            </div>
            <p className="text-sm text-muted-foreground">
              Vi strävar efter att svara på alla förfrågningar inom 2 arbetsdagar.
            </p>
          </div>

          {/* Samarbeten */}
          <div>
            <h2 className="text-2xl font-bold mb-4">Samarbeten</h2>
            <p className="text-muted-foreground mb-6">
              Vi är alltid öppna för spännande samarbeten som kan hjälpa våra läsare att få tillgång till ännu bättre
              guider och information.
            </p>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-8 mb-8">
              <div className="border rounded-lg p-6">
                <h3 className="font-bold text-xl mb-3">För innehållsskapare</h3>
                <p className="text-muted-foreground mb-4">
                  Är du expert inom ett område och vill bidra med guider till vår plattform? Vi välkomnar högkvalitativt
                  innehåll som hjälper våra läsare att lösa vardagliga problem.
                </p>
                <Button asChild variant="outline">
                  <a href="mailto:<EMAIL>?subject=Innehållssamarbete">Kontakta oss</a>
                </Button>
              </div>

              <div className="border rounded-lg p-6">
                <h3 className="font-bold text-xl mb-3">För företag</h3>
                <p className="text-muted-foreground mb-4">
                  Vi erbjuder olika samarbetsmöjligheter för företag som vill nå ut till vår engagerade publik. Kontakta
                  oss för att diskutera sponsrade guider, partnerskap eller andra samarbetsformer.
                </p>
                <Button asChild variant="outline">
                  <a href="mailto:<EMAIL>?subject=Företagssamarbete">Kontakta oss</a>
                </Button>
              </div>
            </div>

            <p className="text-muted-foreground">
              Observera att vi endast ingår samarbeten som vi tror kommer att ge värde till våra läsare. Alla sponsrade
              innehåll markeras tydligt som sådana för fullständig transparens.
            </p>
          </div>
        </div>
      </div>
    </div>
  )
}
