import fs from 'fs'
import path from 'path'
import matter from 'gray-matter'
import { remark } from 'remark'
import html from 'remark-html'
import remarkGfm from 'remark-gfm'
import { Guide } from './types'

const guidesDirectory = path.join(process.cwd(), 'content/guides')

export async function getGuideData(slug: string): Promise<Guide | null> {
  try {
    const fullPath = path.join(guidesDirectory, `${slug}.md`)
    const fileContents = fs.readFileSync(fullPath, 'utf8')

    // Parse the markdown file with gray-matter to get frontmatter and content
    const { data, content } = matter(fileContents)

    // Process the content sections
    const sections = processContentSections(content)

    // Return the guide data
    return {
      slug,
      title: data.title,
      excerpt: data.excerpt,
      date: data.date,
      category: data.category,
      author: data.author || 'Guidebanken',
      image: data.image || data.coverImage || '/images/default-guide.jpg',
      content: {
        introduction: sections.introduction,
        requirements: sections.requirements,
        steps: sections.steps,
        conclusion: sections.conclusion,
        faq: sections.faq,
      },
    }
  } catch (error) {
    console.error(`Error getting guide data for ${slug}:`, error)
    return null
  }
}

export async function getAllGuides(): Promise<Guide[]> {
  try {
    // Get all markdown files from the guides directory
    const fileNames = fs.readdirSync(guidesDirectory)
    const allGuidesData = await Promise.all(
      fileNames
        .filter((fileName) => fileName.endsWith('.md'))
        .map(async (fileName) => {
          // Remove ".md" from file name to get slug
          const slug = fileName.replace(/\.md$/, '')

          // Get guide data
          const guideData = await getGuideData(slug)

          return guideData
        })
    )

    // Filter out any null values and sort by date
    return allGuidesData
      .filter((guide): guide is Guide => guide !== null)
      .sort((a, b) => (new Date(b.date) > new Date(a.date) ? 1 : -1))
  } catch (error) {
    console.error('Error getting all guides:', error)
    return []
  }
}

export async function getGuidesByCategory(category: string): Promise<Guide[]> {
  const guides = await getAllGuides()
  return guides.filter(
    guide => guide.category.toLowerCase() === category.toLowerCase()
  )
}

export async function getAllCategories(): Promise<string[]> {
  const guides = await getAllGuides()
  const categories = [...new Set(guides.map(guide => guide.category))]
  return categories.sort()
}

// Helper function to process content sections from markdown
function processContentSections(content: string) {
  const sections = {
    introduction: '',
    requirements: [] as string[],
    steps: [] as { title: string; description: string }[],
    conclusion: '',
    faq: [] as { question: string; answer: string }[],
  }

  // Split content by sections
  const lines = content.split('\n')
  let currentSection = ''
  let currentStep: { title: string; description: string } | null = null
  let currentFaq: { question: string; answer: string } | null = null

  for (let i = 0; i < lines.length; i++) {
    const line = lines[i].trim()

    // Determine section
    if (line.startsWith('## Introduktion')) {
      currentSection = 'introduction'
      continue
    } else if (line.startsWith('## Vad du behöver')) {
      currentSection = 'requirements'
      continue
    } else if (line.startsWith('## Steg-för-steg instruktioner')) {
      currentSection = 'steps'
      continue
    } else if (line.startsWith('## Sammanfattning')) {
      currentSection = 'conclusion'
      continue
    } else if (line.startsWith('## Vanliga frågor')) {
      currentSection = 'faq'
      continue
    }

    // Process content based on current section
    if (currentSection === 'introduction') {
      if (line && !line.startsWith('#')) {
        sections.introduction += line + ' '
      }
    } else if (currentSection === 'requirements') {
      if (line.startsWith('- ')) {
        sections.requirements.push(line.substring(2))
      }
    } else if (currentSection === 'steps') {
      if (line.startsWith('### Steg ')) {
        // Save previous step if exists
        if (currentStep) {
          sections.steps.push(currentStep)
        }

        // Extract step title
        const titleMatch = line.match(/### Steg \d+: (.+)/)
        if (titleMatch) {
          currentStep = { title: titleMatch[1], description: '' }
        }
      } else if (currentStep && line && !line.startsWith('#')) {
        currentStep.description += line + ' '
      }
    } else if (currentSection === 'conclusion') {
      if (line && !line.startsWith('#')) {
        sections.conclusion += line + ' '
      }
    } else if (currentSection === 'faq') {
      if (line.startsWith('### ')) {
        // Save previous FAQ if exists
        if (currentFaq) {
          sections.faq.push(currentFaq)
        }

        // Start new FAQ
        currentFaq = { question: line.substring(4), answer: '' }
      } else if (currentFaq && line && !line.startsWith('#')) {
        currentFaq.answer += line + ' '
      }
    }
  }

  // Add the last step and FAQ if they exist
  if (currentStep) {
    sections.steps.push(currentStep)
  }
  if (currentFaq) {
    sections.faq.push(currentFaq)
  }

  // Trim all text fields
  sections.introduction = sections.introduction.trim()
  sections.conclusion = sections.conclusion.trim()
  sections.steps.forEach(step => {
    step.description = step.description.trim()
  })
  sections.faq.forEach(faq => {
    faq.answer = faq.answer.trim()
  })

  return sections
}

export async function markdownToHtml(markdown: string) {
  const result = await remark()
    .use(remarkGfm)
    .use(html, { sanitize: false })
    .process(markdown)
  return result.toString()
}
