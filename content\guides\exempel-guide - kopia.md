---
title: "Exempel på ny guide med uppdaterat frontmatter"
excerpt: "Detta är en exempelguide som visar det nya frontmatter-formatet för Guidebanken."
date: 2024-12-15
category: "GUULD"
author: "GULDIS"
image: "/images/exempel-guide.jpg"
---

## Introduktion

Detta är en exempelguide som visar hur det nya frontmatter-formatet fungerar. Frontmatter-fälten har uppdaterats för att vara mer flexibla och enklare att använda.

## Vad du behöver

- En texteditor
- Grundläggande kunskap om Markdown
- Förståelse för YAML frontmatter

## Steg-för-steg instruktioner

### Steg 1: Skapa frontmatter

Börja din guide med frontmatter-sektionen som innehåller metadata om guiden. Använd det nya formatet med title, excerpt, date, category, author och image.

### Steg 2: Skriv introduktion

Skriv en kort introduktion som förklarar vad guiden handlar om och vad läsaren kommer att lära sig.

### Steg 3: Lista vad som behövs

Skapa en lista över vad läsaren behöver för att följa guiden.

### Steg 4: Skriv steg-för-steg instruktioner

Dela upp instruktionerna i tydliga steg med beskrivande rubriker.

### Steg 5: Avsluta med sammanfattning

Sammanfatta det viktigaste från guiden.

## Sammanfattning

Det nya frontmatter-formatet gör det enklare att hantera metadata för guider. Alla fält är nu obligatoriska och har ett konsekvent format.

## Vanliga frågor

### Vad är skillnaden från det gamla formatet?

Det nya formatet tar bort readingTime-fältet och byter namn på coverImage till image. Det lägger också till author-fältet.

### Måste jag uppdatera alla befintliga guider?

Nej, systemet har bakåtkompatibilitet och kommer att hantera både gamla och nya format.
