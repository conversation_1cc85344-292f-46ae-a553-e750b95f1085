"use client"

import { useState, useEffect } from "react"
import Link from "next/link"
import { Button } from "@/components/ui/button"

export default function CookieBanner() {
  const [isVisible, setIsVisible] = useState(false)

  useEffect(() => {
    // Check if user has already accepted cookies
    const cookiesAccepted = localStorage.getItem("cookiesAccepted")
    if (!cookiesAccepted) {
      setIsVisible(true)
    }
  }, [])

  const acceptCookies = () => {
    localStorage.setItem("cookiesAccepted", "true")
    setIsVisible(false)
  }

  if (!isVisible) {
    return null
  }

  return (
    <div className="fixed bottom-0 left-0 right-0 z-50 bg-background border-t p-4 md:p-6 shadow-lg">
      <div className="container mx-auto flex flex-col sm:flex-row items-center justify-between gap-4">
        <div>
          <p className="text-sm text-muted-foreground">
            Vi använder cookies för att förbättra din upplevelse på vår webbplats. Genom att fortsätta använda vår
            webbplats godkänner du vår användning av cookies. Läs vår{" "}
            <Link href="/cookies" className="text-primary hover:underline">
              cookiepolicy
            </Link>{" "}
            för mer information.
          </p>
        </div>
        <div className="flex gap-4">
          <Button variant="outline" size="sm" asChild>
            <Link href="/cookies">Läs mer</Link>
          </Button>
          <Button size="sm" onClick={acceptCookies}>
            Acceptera
          </Button>
        </div>
      </div>
    </div>
  )
}
