import type { <PERSON>ada<PERSON> } from "next"
import Image from "next/image"
import Link from "next/link"
import { notFound } from "next/navigation"
import { ArrowLeft } from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"
import { getGuides } from "@/lib/guides-data"
import { getGuideData } from "@/lib/markdown"

export async function generateStaticParams() {
  const guides = await getGuides()
  return guides.map((guide) => ({
    slug: guide.slug,
  }))
}

export async function generateMetadata({ params }: { params: Promise<{ slug: string }> }): Promise<Metadata> {
  const { slug } = await params
  const guide = await getGuideData(slug)

  if (!guide) {
    return {
      title: "Guide hittades inte",
      description: "Den begärda guiden kunde inte hittas.",
    }
  }

  return {
    title: guide.title,
    description: guide.excerpt,
    keywords: [...guide.title.toLowerCase().split(" "), "guide", "hur gör man", "steg-för-steg"],
    openGraph: {
      title: guide.title,
      description: guide.excerpt,
      type: "article",
      url: `https://guidebanken.se/guider/${guide.slug}`,
    },
  }
}

export default async function GuidePage({ params }: { params: Promise<{ slug: string }> }) {
  const { slug } = await params
  const guide = await getGuideData(slug)

  if (!guide) {
    notFound()
  }

  return (
    <article className="container mx-auto px-4 md:px-6 py-12">
      <Button asChild variant="ghost" className="mb-8 hover:bg-primary/10">
        <Link href="/guider" className="flex items-center gap-2">
          <ArrowLeft className="h-4 w-4" /> Tillbaka till alla guider
        </Link>
      </Button>

      <div className="max-w-3xl mx-auto">
        <div className="mb-10">
          <Link
            href={`/kategorier/${guide.category.toLowerCase().replace(/\s+/g, '-')}`}
            className="inline-block rounded-full bg-primary/10 px-3 py-1 text-xs font-medium text-primary mb-4 hover:bg-primary/20 transition-colors"
          >
            {guide.category}
          </Link>
          <h1 className="text-4xl md:text-5xl font-bold tracking-tight mb-6 font-serif">{guide.title}</h1>
          <p className="text-xl text-muted-foreground mb-6">{guide.excerpt}</p>
          <div className="flex items-center gap-4 text-sm text-muted-foreground">
            <time dateTime={guide.date} className="flex items-center">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                <rect x="3" y="4" width="18" height="18" rx="2" ry="2"></rect>
                <line x1="16" y1="2" x2="16" y2="6"></line>
                <line x1="8" y1="2" x2="8" y2="6"></line>
                <line x1="3" y1="10" x2="21" y2="10"></line>
              </svg>
              {new Date(guide.date).toLocaleDateString("sv-SE", { year: "numeric", month: "long", day: "numeric" })}
            </time>
            <span>•</span>
            <span className="flex items-center">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                <circle cx="12" cy="12" r="10"></circle>
                <polyline points="12 6 12 12 16 14"></polyline>
              </svg>
              {guide.readingTime} min läsning
            </span>
          </div>
        </div>

        <div className="relative w-full aspect-video rounded-lg overflow-hidden mb-10 shadow-md">
          <Image src={guide.coverImage || "/placeholder.svg"} alt={guide.title} fill className="object-cover" />
        </div>

        <div className="prose prose-lg max-w-none prose-headings:font-serif prose-headings:font-bold prose-h2:text-2xl prose-h3:text-xl prose-p:text-base prose-p:leading-relaxed prose-li:text-base prose-li:leading-relaxed">
          <h2>Introduktion</h2>
          <p>{guide.content.introduction}</p>

          <h2>Vad du behöver</h2>
          <ul>
            {guide.content.requirements.map((req, index) => (
              <li key={index}>{req}</li>
            ))}
          </ul>

          <h2>Steg-för-steg instruktioner</h2>
          {guide.content.steps.map((step, index) => (
            <div key={index} className="mb-6">
              <h3>
                Steg {index + 1}: {step.title}
              </h3>
              <p>{step.description}</p>
            </div>
          ))}

          <h2>Sammanfattning</h2>
          <p>{guide.content.conclusion}</p>

          <h2>Vanliga frågor</h2>
          {guide.content.faq.map((item, index) => (
            <div key={index} className="mb-4">
              <h3>{item.question}</h3>
              <p>{item.answer}</p>
            </div>
          ))}
        </div>
      </div>
    </article>
  )
}
