import type { <PERSON><PERSON><PERSON> } from "next"
import Link from "next/link"
import Image from "next/image"
import { ArrowRight } from "lucide-react"
import FeaturedGuides from "@/components/featured-guides"
import { Button } from "@/components/ui/button"
import { getGuides } from "@/lib/guides-data"

export const metadata: Metadata = {
  title: "Guidebanken - Din källa för praktiska guider och tips",
  description:
    "Upptäck praktiska guider och steg-för-steg instruktioner för att lösa vardagliga problem och lära dig nya färdigheter på Guidebanken.",
  keywords: ["guidebanken", "guider", "hur gör man", "steg-för-steg", "instruktioner", "tips", "hjälp", "lösningar"],
}

export default async function Home() {
  // Get all guides and take the first 6 for featured section
  const guides = await getGuides()
  const featuredGuides = guides.slice(0, 6)

  return (
    <div className="flex flex-col gap-16 pb-16">
      {/* Hero Section */}
      <section className="relative w-full h-[75vh] min-h-[600px] overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-r from-black/70 to-black/40 z-10" />
        <Image
          src="/images/new-hero-image.jpg"
          alt="Guidebanken - Din källa för praktiska guider"
          fill
          priority
          className="object-cover"
        />
        <div className="relative z-20 container mx-auto h-full flex flex-col justify-center items-start px-4 md:px-6">
          <div className="inline-block rounded-full bg-green-600/90 px-4 py-1.5 text-sm font-medium text-white mb-6 backdrop-blur-sm shadow-lg">
            Din källa för praktiska guider
          </div>
          <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold text-white max-w-3xl mb-6 font-serif drop-shadow-lg">
            Hitta lösningar på <span className="text-green-400">vardagliga problem</span>
          </h1>
          <p className="text-xl md:text-2xl text-white/95 max-w-2xl mb-10 leading-relaxed drop-shadow-md">
            Guidebanken erbjuder praktiska guider och steg-för-steg instruktioner för att hjälpa dig i vardagen.
          </p>
          <div className="flex flex-col sm:flex-row gap-4">
            <Button asChild size="lg" className="text-base px-8 py-6 bg-green-600 hover:bg-green-700 text-white shadow-lg font-medium">
              <Link href="/guider">Utforska våra guider</Link>
            </Button>
            <Button asChild size="lg" className="text-base px-8 py-6 bg-white/10 hover:bg-white/20 text-white border-2 border-white/30 backdrop-blur-sm shadow-lg font-medium">
              <Link href="/kategorier">Bläddra kategorier</Link>
            </Button>
          </div>
        </div>
      </section>

      {/* Featured Guides Section */}
      <section className="container mx-auto px-4 md:px-6">
        <div className="flex flex-col md:flex-row md:items-center justify-between mb-12 gap-4">
          <div>
            <div className="inline-block rounded-full bg-primary/10 px-3 py-1 text-xs font-medium text-primary mb-3">
              Utvalda
            </div>
            <h2 className="text-3xl md:text-4xl font-bold tracking-tight font-serif">Populära guider</h2>
            <p className="text-muted-foreground mt-2 text-lg">Utforska våra mest populära steg-för-steg guider</p>
          </div>
          <div className="flex flex-col sm:flex-row gap-4">
            <Button asChild variant="outline" className="w-fit hover:bg-primary/10 border-primary/20">
              <Link href="/guider" className="flex items-center gap-2">
                Visa alla guider <ArrowRight className="h-4 w-4" />
              </Link>
            </Button>
            <Button asChild variant="ghost" className="w-fit hover:bg-secondary">
              <Link href="/kategorier" className="flex items-center gap-2">
                Bläddra efter kategori <ArrowRight className="h-4 w-4" />
              </Link>
            </Button>
          </div>
        </div>
        <FeaturedGuides guides={featuredGuides} />
      </section>

      {/* Why Choose Us Section */}
      <section className="bg-secondary py-24">
        <div className="container mx-auto px-4 md:px-6">
          <div className="text-center mb-16">
            <div className="inline-block rounded-full bg-primary/10 px-3 py-1 text-xs font-medium text-primary mb-3">
              Fördelar
            </div>
            <h2 className="text-3xl md:text-4xl font-bold tracking-tight font-serif mb-4">Varför välja Guidebanken?</h2>
            <p className="text-muted-foreground max-w-2xl mx-auto text-lg">
              Vi strävar efter att erbjuda de bästa guiderna för att hjälpa dig lösa vardagliga problem
            </p>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <div className="bg-background rounded-xl p-8 shadow-md hover:shadow-lg transition-all duration-300 border border-border/50">
              <div className="h-14 w-14 rounded-full bg-primary/10 flex items-center justify-center mb-6">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="28"
                  height="28"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  className="text-primary"
                >
                  <path d="M12 20h9"></path>
                  <path d="M16.5 3.5a2.121 2.121 0 0 1 3 3L7 19l-4 1 1-4L16.5 3.5z"></path>
                </svg>
              </div>
              <h3 className="text-xl font-bold mb-3 font-serif">Tydliga instruktioner</h3>
              <p className="text-muted-foreground leading-relaxed">
                Våra guider är skrivna med tydliga steg-för-steg instruktioner som är lätta att följa, även för nybörjare.
              </p>
            </div>
            <div className="bg-background rounded-xl p-8 shadow-md hover:shadow-lg transition-all duration-300 border border-border/50">
              <div className="h-14 w-14 rounded-full bg-primary/10 flex items-center justify-center mb-6">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="28"
                  height="28"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  className="text-primary"
                >
                  <path d="M14 9V5a3 3 0 0 0-3-3l-4 9v11h11.28a2 2 0 0 0 2-1.7l1.38-9a2 2 0 0 0-2-2.3zM7 22H4a2 2 0 0 1-2-2v-7a2 2 0 0 1 2-2h3"></path>
                </svg>
              </div>
              <h3 className="text-xl font-bold mb-3 font-serif">Expertkunskap</h3>
              <p className="text-muted-foreground leading-relaxed">
                Alla våra guider är skrivna av experter inom sina respektive områden med års av praktisk erfarenhet.
              </p>
            </div>
            <div className="bg-background rounded-xl p-8 shadow-md hover:shadow-lg transition-all duration-300 border border-border/50">
              <div className="h-14 w-14 rounded-full bg-primary/10 flex items-center justify-center mb-6">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="28"
                  height="28"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  className="text-primary"
                >
                  <path d="M12 22s8-4 8-10V5l-8-3-8 3v7c0 6 8 10 8 10z"></path>
                </svg>
              </div>
              <h3 className="text-xl font-bold mb-3 font-serif">Pålitlig information</h3>
              <p className="text-muted-foreground leading-relaxed">
                Vi uppdaterar regelbundet våra guider för att säkerställa att informationen är aktuell, korrekt och relevant.
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Latest Guides Section */}
      <section className="container mx-auto px-4 md:px-6">
        <div className="text-center mb-12">
          <div className="inline-block rounded-full bg-green-600/10 px-3 py-1 text-xs font-medium text-green-600 mb-3">
            Senaste
          </div>
          <h2 className="text-3xl md:text-4xl font-bold tracking-tight font-serif mb-4">Nyligen tillagda guider</h2>
          <p className="text-muted-foreground max-w-2xl mx-auto text-lg">
            Upptäck våra senaste guider och håll dig uppdaterad med nya lösningar
          </p>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {featuredGuides.slice(0, 3).map((guide, index) => (
            <div key={index} className="group bg-background rounded-xl border border-border/50 overflow-hidden hover:shadow-lg transition-all duration-300 hover:border-green-200">
              <div className="aspect-video relative overflow-hidden">
                <Image
                  src={guide.image}
                  alt={guide.title}
                  fill
                  className="object-cover group-hover:scale-105 transition-transform duration-300"
                />
                <div className="absolute top-3 left-3">
                  <span className="inline-block rounded-full bg-green-600 px-2 py-1 text-xs font-medium text-white">
                    {guide.category}
                  </span>
                </div>
              </div>
              <div className="p-6">
                <h3 className="text-xl font-bold mb-2 font-serif group-hover:text-green-600 transition-colors">
                  {guide.title}
                </h3>
                <p className="text-muted-foreground text-sm mb-4 line-clamp-2">
                  {guide.excerpt}
                </p>
                <div className="flex items-center justify-between text-sm text-muted-foreground">
                  <span>Av {guide.author}</span>
                  <span>{new Date(guide.date).toLocaleDateString('sv-SE')}</span>
                </div>
              </div>
            </div>
          ))}
        </div>
      </section>

      {/* Stats & Trust Section */}
      <section className="bg-gradient-to-br from-green-50 to-green-100 py-24">
        <div className="container mx-auto px-4 md:px-6">
          <div className="text-center mb-16">
            <div className="inline-block rounded-full bg-green-600/10 px-3 py-1 text-xs font-medium text-green-600 mb-3">
              Trovärdighet
            </div>
            <h2 className="text-3xl md:text-4xl font-bold tracking-tight font-serif mb-4">Betrodd av tusentals användare</h2>
            <p className="text-muted-foreground max-w-2xl mx-auto text-lg">
              Våra guider har hjälpt människor över hela Sverige att lösa vardagliga problem
            </p>
          </div>

          <div className="grid grid-cols-2 md:grid-cols-4 gap-8 mb-16">
            <div className="text-center">
              <div className="text-4xl md:text-5xl font-bold text-green-600 mb-2">50+</div>
              <div className="text-muted-foreground font-medium">Praktiska guider</div>
            </div>
            <div className="text-center">
              <div className="text-4xl md:text-5xl font-bold text-green-600 mb-2">10k+</div>
              <div className="text-muted-foreground font-medium">Nöjda användare</div>
            </div>
            <div className="text-center">
              <div className="text-4xl md:text-5xl font-bold text-green-600 mb-2">95%</div>
              <div className="text-muted-foreground font-medium">Lösta problem</div>
            </div>
            <div className="text-center">
              <div className="text-4xl md:text-5xl font-bold text-green-600 mb-2">24/7</div>
              <div className="text-muted-foreground font-medium">Tillgängliga guider</div>
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            <div className="bg-white rounded-xl p-8 shadow-sm border border-green-100">
              <div className="flex items-start gap-4">
                <div className="h-12 w-12 rounded-full bg-green-100 flex items-center justify-center flex-shrink-0">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    width="24"
                    height="24"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    strokeWidth="2"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    className="text-green-600"
                  >
                    <polygon points="12 2 15.09 8.26 22 9.27 17 14.14 18.18 21.02 12 17.77 5.82 21.02 7 14.14 2 9.27 8.91 8.26 12 2"></polygon>
                  </svg>
                </div>
                <div>
                  <h3 className="text-lg font-bold mb-2">Kvalitetsgaranti</h3>
                  <p className="text-muted-foreground">
                    Alla våra guider genomgår noggrann kvalitetskontroll och testas av vårt expertteam innan publicering.
                  </p>
                </div>
              </div>
            </div>

            <div className="bg-white rounded-xl p-8 shadow-sm border border-green-100">
              <div className="flex items-start gap-4">
                <div className="h-12 w-12 rounded-full bg-green-100 flex items-center justify-center flex-shrink-0">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    width="24"
                    height="24"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    strokeWidth="2"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    className="text-green-600"
                  >
                    <path d="M12 22s8-4 8-10V5l-8-3-8 3v7c0 6 8 10 8 10z"></path>
                  </svg>
                </div>
                <div>
                  <h3 className="text-lg font-bold mb-2">Säker information</h3>
                  <p className="text-muted-foreground">
                    Vi uppdaterar kontinuerligt vårt innehåll för att säkerställa att all information är aktuell och säker att följa.
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

    </div>
  )
}
