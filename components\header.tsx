"use client"

import { useState } from "react"
import Link from "next/link"
import Image from "next/image"
import { Menu, X } from "lucide-react"
import { Button } from "@/components/ui/button"

export default function Header() {
  const [isMenuOpen, setIsMenuOpen] = useState(false)

  return (
    <header className="sticky top-0 z-50 w-full bg-gradient-to-r from-white to-accent/30 shadow-md">
      <div className="container flex h-20 items-center justify-between px-4 md:px-6">
        <Link href="/" className="flex items-center gap-2 transition-transform hover:scale-105">
          <div className="flex items-center">
            <div className="relative h-10 w-10 mr-3">
              <Image
                src="/images/guidebanken-logo.png"
                alt="Guidebanken logotyp"
                fill
                className="object-contain"
                priority
              />
            </div>
            <span className="font-bold text-2xl text-primary">Guidebanken</span>
          </div>
        </Link>

        <nav className="hidden md:flex items-center gap-6">
          <Link href="/guider" className="text-base font-medium text-primary/90 hover:text-primary transition-colors px-3 py-2">
            Guider
          </Link>
          <Link href="/kategorier" className="text-base font-medium text-primary/90 hover:text-primary transition-colors px-3 py-2">
            Kategorier
          </Link>
          <Link href="/kontakt" className="text-base font-medium text-primary/90 hover:text-primary transition-colors px-3 py-2">
            Kontakt
          </Link>
        </nav>

        <div className="flex items-center gap-4">
          <Button variant="ghost" size="icon" className="md:hidden text-primary hover:bg-primary/10" onClick={() => setIsMenuOpen(true)}>
            <Menu className="h-6 w-6" />
            <span className="sr-only">Öppna meny</span>
          </Button>
        </div>

        {isMenuOpen && (
          <div className="fixed inset-0 z-50 bg-gradient-to-r from-white to-accent/30 md:hidden">
            <div className="container flex h-20 items-center justify-between px-4">
              <Link href="/" className="flex items-center gap-2">
                <div className="flex items-center">
                  <div className="relative h-10 w-10 mr-3">
                    <Image
                      src="/images/guidebanken-logo.png"
                      alt="Guidebanken logotyp"
                      fill
                      className="object-contain"
                      priority
                    />
                  </div>
                  <span className="font-bold text-2xl text-primary">Guidebanken</span>
                </div>
              </Link>
              <Button variant="ghost" size="icon" className="text-primary hover:bg-primary/10" onClick={() => setIsMenuOpen(false)}>
                <X className="h-6 w-6" />
                <span className="sr-only">Stäng meny</span>
              </Button>
            </div>
            <div className="container px-4 py-6">
              <nav className="grid gap-6 mt-4">
                <Link
                  href="/guider"
                  className="flex items-center text-xl font-medium text-primary/90 hover:text-primary transition-colors border-b border-primary/10 pb-4"
                  onClick={() => setIsMenuOpen(false)}
                >
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-3" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                    <path d="M4 19.5v-15A2.5 2.5 0 0 1 6.5 2H20v20H6.5a2.5 2.5 0 0 1 0-5H20"></path>
                  </svg>
                  Guider
                </Link>
                <Link
                  href="/kategorier"
                  className="flex items-center text-xl font-medium text-primary/90 hover:text-primary transition-colors border-b border-primary/10 pb-4"
                  onClick={() => setIsMenuOpen(false)}
                >
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-3" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                    <rect x="3" y="3" width="7" height="7"></rect>
                    <rect x="14" y="3" width="7" height="7"></rect>
                    <rect x="14" y="14" width="7" height="7"></rect>
                    <rect x="3" y="14" width="7" height="7"></rect>
                  </svg>
                  Kategorier
                </Link>
                <Link
                  href="/kontakt"
                  className="flex items-center text-xl font-medium text-primary/90 hover:text-primary transition-colors border-b border-primary/10 pb-4"
                  onClick={() => setIsMenuOpen(false)}
                >
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-3" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                    <path d="M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z"></path>
                  </svg>
                  Kontakt
                </Link>
              </nav>
            </div>
          </div>
        )}
      </div>
    </header>
  )
}
