import Link from "next/link"
import Image from "next/image"
import { Mail, MapPin, Clock } from "lucide-react"

export default function Footer() {
  return (
    <footer className="w-full bg-gradient-to-br from-slate-50 to-green-50 border-t border-green-100">
      <div className="container px-4 md:px-6 py-16 md:py-20">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-12">
          {/* Brand Section */}
          <div className="lg:col-span-2">
            <Link href="/" className="flex items-center gap-2 mb-6 group">
              <div className="flex items-center">
                <div className="relative h-10 w-10 mr-3">
                  <Image
                    src="/images/guidebanken-logo.png"
                    alt="Guidebanken logotyp"
                    fill
                    className="object-contain group-hover:scale-110 transition-transform duration-300"
                  />
                </div>
                <span className="font-bold text-3xl text-primary">Guide<span className="text-green-600">banken</span></span>
              </div>
            </Link>
            <p className="text-muted-foreground max-w-lg text-base leading-relaxed mb-6">
              Guidebanken erbjuder praktiska guider och steg-för-steg instruktioner för att hjälpa dig lösa vardagliga
              problem och lära dig nya färdigheter. Vår mission är att göra kunskap tillgänglig för alla.
            </p>

            {/* Contact Info */}
            <div className="space-y-3">
              <div className="flex items-center gap-3 text-muted-foreground">
                <Mail className="h-4 w-4 text-green-600" />
                <span className="text-sm"><EMAIL></span>
              </div>
              <div className="flex items-center gap-3 text-muted-foreground">
                <MapPin className="h-4 w-4 text-green-600" />
                <span className="text-sm">Stockholm, Sverige</span>
              </div>
              <div className="flex items-center gap-3 text-muted-foreground">
                <Clock className="h-4 w-4 text-green-600" />
                <span className="text-sm">Uppdateras dagligen</span>
              </div>
            </div>
          </div>

          {/* Navigation Links */}
          <div>
            <h3 className="font-serif font-bold text-lg mb-6 text-primary">Navigering</h3>
            <nav className="flex flex-col gap-4">
              <Link href="/" className="text-muted-foreground hover:text-green-600 transition-colors duration-200 text-sm font-medium">
                Startsida
              </Link>
              <Link href="/guider" className="text-muted-foreground hover:text-green-600 transition-colors duration-200 text-sm font-medium">
                Alla guider
              </Link>
              <Link href="/kategorier" className="text-muted-foreground hover:text-green-600 transition-colors duration-200 text-sm font-medium">
                Kategorier
              </Link>
              <Link href="/kontakt" className="text-muted-foreground hover:text-green-600 transition-colors duration-200 text-sm font-medium">
                Kontakt
              </Link>
            </nav>
          </div>

          {/* Legal & Support */}
          <div>
            <h3 className="font-serif font-bold text-lg mb-6 text-primary">Support & Juridiskt</h3>
            <nav className="flex flex-col gap-4">
              <Link href="/integritetspolicy" className="text-muted-foreground hover:text-green-600 transition-colors duration-200 text-sm font-medium">
                Integritetspolicy
              </Link>
              <Link href="/cookies" className="text-muted-foreground hover:text-green-600 transition-colors duration-200 text-sm font-medium">
                Cookiepolicy
              </Link>
              <Link href="/kontakt" className="text-muted-foreground hover:text-green-600 transition-colors duration-200 text-sm font-medium">
                Hjälp & Support
              </Link>
              <span className="text-muted-foreground text-sm">
                Feedback välkomnas
              </span>
            </nav>
          </div>
        </div>

        {/* Bottom Section */}
        <div className="flex flex-col lg:flex-row justify-between items-center mt-16 pt-8 border-t border-green-200">
          <div className="flex flex-col sm:flex-row items-center gap-4 mb-6 lg:mb-0">
            <p className="text-sm text-muted-foreground">
              &copy; {new Date().getFullYear()} Guidebanken. Alla rättigheter förbehållna.
            </p>
            <div className="hidden sm:block w-1 h-1 bg-muted-foreground/30 rounded-full"></div>
            <p className="text-sm text-muted-foreground">
              Gjord med ❤️ i Sverige
            </p>
          </div>

          {/* Quality Badges */}
          <div className="flex items-center gap-6">
            <div className="flex items-center gap-2 text-green-600">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="16"
                height="16"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
              >
                <path d="M12 22s8-4 8-10V5l-8-3-8 3v7c0 6 8 10 8 10z"></path>
              </svg>
              <span className="text-xs font-medium">Säker information</span>
            </div>
            <div className="flex items-center gap-2 text-green-600">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="16"
                height="16"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
              >
                <polygon points="12 2 15.09 8.26 22 9.27 17 14.14 18.18 21.02 12 17.77 5.82 21.02 7 14.14 2 9.27 8.91 8.26 12 2"></polygon>
              </svg>
              <span className="text-xs font-medium">Kvalitetsgranskad</span>
            </div>
          </div>
        </div>
      </div>
    </footer>
  )
}
