import Link from "next/link"
import Image from "next/image"

export default function Footer() {
  return (
    <footer className="w-full border-t bg-background">
      <div className="container px-4 md:px-6 py-12 md:py-16">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-10">
          <div className="md:col-span-2">
            <Link href="/" className="flex items-center gap-2 mb-6">
              <div className="flex items-center">
                <div className="relative h-8 w-8 mr-2">
                  <Image
                    src="/images/guidebanken-logo.png"
                    alt="Guidebanken logotyp"
                    fill
                    className="object-contain"
                  />
                </div>
                <span className="font-bold text-2xl text-primary">Guide<span className="text-accent">banken</span></span>
              </div>
            </Link>
            <p className="text-muted-foreground max-w-md text-base leading-relaxed">
              Guidebanken erbjuder praktiska guider och steg-för-steg instruktioner för att hjälpa dig lösa vardagliga
              problem och lära dig nya färdigheter. Vår mission är att göra kunskap tillgänglig för alla.
            </p>
          </div>
          <div>
            <h3 className="font-serif font-bold text-lg mb-6">Snabblänkar</h3>
            <nav className="flex flex-col gap-3">
              <Link href="/" className="text-muted-foreground hover:text-primary transition-colors">
                Hem
              </Link>
              <Link href="/guider" className="text-muted-foreground hover:text-primary transition-colors">
                Guider
              </Link>
              <Link href="/kategorier" className="text-muted-foreground hover:text-primary transition-colors">
                Kategorier
              </Link>
              <Link href="/kontakt" className="text-muted-foreground hover:text-primary transition-colors">
                Kontakt
              </Link>
            </nav>
          </div>
          <div>
            <h3 className="font-serif font-bold text-lg mb-6">Juridiskt</h3>
            <nav className="flex flex-col gap-3">
              <Link href="/integritetspolicy" className="text-muted-foreground hover:text-primary transition-colors">
                Integritetspolicy
              </Link>
              <Link href="/cookies" className="text-muted-foreground hover:text-primary transition-colors">
                Cookiepolicy
              </Link>
            </nav>
          </div>
        </div>
        <div className="flex flex-col md:flex-row justify-between items-center mt-12 pt-8 border-t">
          <p className="text-sm text-muted-foreground">
            &copy; {new Date().getFullYear()} Guidebanken. Alla rättigheter förbehållna.
          </p>
          <div className="flex gap-6 mt-6 md:mt-0">
            <Link href="#" className="text-muted-foreground hover:text-primary transition-colors">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="24"
                height="24"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
                className="h-5 w-5"
              >
                <path d="M18 2h-3a5 5 0 0 0-5 5v3H7v4h3v8h4v-8h3l1-4h-4V7a1 1 0 0 1 1-1h3z"></path>
              </svg>
              <span className="sr-only">Facebook</span>
            </Link>
            <Link href="#" className="text-muted-foreground hover:text-primary transition-colors">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="24"
                height="24"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
                className="h-5 w-5"
              >
                <rect x="2" y="2" width="20" height="20" rx="5" ry="5"></rect>
                <path d="M16 11.37A4 4 0 1 1 12.63 8 4 4 0 0 1 16 11.37z"></path>
                <line x1="17.5" y1="6.5" x2="17.51" y2="6.5"></line>
              </svg>
              <span className="sr-only">Instagram</span>
            </Link>
            <Link href="#" className="text-muted-foreground hover:text-primary transition-colors">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="24"
                height="24"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
                className="h-5 w-5"
              >
                <path d="M22 4s-.7 2.1-2 3.4c1.6 10-9.4 17.3-18 11.6 2.2.1 4.4-.6 6-2C3 15.5.5 9.6 3 5c2.2 2.6 5.6 4.1 9 4-.9-4.2 4-6.6 7-3.8 1.1 0 3-1.2 3-1.2z"></path>
              </svg>
              <span className="sr-only">Twitter</span>
            </Link>
          </div>
        </div>
      </div>
    </footer>
  )
}
