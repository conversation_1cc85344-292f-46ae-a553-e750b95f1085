import type { Metada<PERSON> } from "next"
import Link from "next/link"
import { notFound } from "next/navigation"
import { ArrowLeft } from "lucide-react"
import { getGuides, getCategories, getGuidesByCategory } from "@/lib/guides-data"
import GuideCard from "@/components/guide-card"
import { Button } from "@/components/ui/button"

interface CategoryPageProps {
  params: {
    category: string
  }
}

export async function generateStaticParams() {
  const categories = await getCategories()

  return categories.map(category => ({
    category: category.toLowerCase().replace(/\s+/g, '-')
  }))
}

export async function generateMetadata({ params }: CategoryPageProps): Promise<Metadata> {
  const decodedCategory = decodeURIComponent(params.category)
  const formattedCategory = decodedCategory
    .split('-')
    .map(word => word.charAt(0).toUpperCase() + word.slice(1))
    .join(' ')

  return {
    title: `${formattedCategory} - Guider och tips`,
    description: `Utforska våra guider inom ${formattedCategory.toLowerCase()} med steg-för-steg instruktioner och praktiska tips.`,
    keywords: [formattedCategory, "guider", "hur gör man", "steg-för-steg", "instruktioner", "tips"],
  }
}

export default async function CategoryPage({ params }: CategoryPageProps) {
  // Convert URL-friendly category to display format
  const decodedCategory = decodeURIComponent(params.category)
  const formattedCategory = decodedCategory
    .split('-')
    .map(word => word.charAt(0).toUpperCase() + word.slice(1))
    .join(' ')

  // Get guides for this category
  const categoryGuides = await getGuidesByCategory(formattedCategory)

  // If no guides found or invalid category, return 404
  if (categoryGuides.length === 0) {
    notFound()
  }

  return (
    <div className="container mx-auto px-4 md:px-6 py-12">
      <Button asChild variant="ghost" className="mb-8 hover:bg-primary/10">
        <Link href="/kategorier" className="flex items-center gap-2">
          <ArrowLeft className="h-4 w-4" /> Tillbaka till alla kategorier
        </Link>
      </Button>

      <div className="max-w-3xl mx-auto text-center mb-12">
        <div className="inline-block rounded-full bg-primary/10 px-3 py-1 text-xs font-medium text-primary mb-3">
          Kategori
        </div>
        <h1 className="text-4xl font-bold tracking-tight mb-4 font-serif">{formattedCategory}</h1>
        <p className="text-xl text-muted-foreground">
          Utforska våra {categoryGuides.length} guider inom {formattedCategory.toLowerCase()}.
        </p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {categoryGuides.map((guide) => (
          <GuideCard key={guide.slug} guide={guide} />
        ))}
      </div>
    </div>
  )
}
