import type React from "react"
import type { <PERSON><PERSON><PERSON> } from "next"
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "next/font/google"
import "./globals.css"
import Header from "@/components/header"
import Footer from "@/components/footer"
import CookieBanner from "@/components/cookie-banner"
import { Toaster } from "@/components/ui/toaster"

const montserrat = Montserrat({
  subsets: ["latin"],
  weight: ["400", "500", "600", "700"],
  variable: "--font-montserrat",
})

const merriweather = Merriweather({
  subsets: ["latin"],
  weight: ["400", "700"],
  variable: "--font-merriweather",
})

export const metadata: Metadata = {
  metadataBase: new URL("https://guidebanken.se"),
  title: {
    default: "Guidebanken - Din källa för praktiska guider och tips",
    template: "%s | Guidebanken",
  },
  description:
    "Guidebanken erbjuder praktiska guider och steg-för-steg instruktioner för att hj<PERSON><PERSON>pa dig lösa vardagliga problem och lära dig nya färdigheter.",
  keywords: ["guider", "hur gör man", "steg-för-steg", "instruktioner", "tips", "hjälp", "lösningar"],
  authors: [{ name: "Guidebanken" }],
  creator: "Guidebanken",
  publisher: "Guidebanken",
  icons: {
    icon: [
      { url: "/favicon.ico" },
      { url: "/images/guidebanken-logo.png", sizes: "512x512", type: "image/png" }
    ],
    apple: "/images/guidebanken-logo.png",
  },
  openGraph: {
    type: "website",
    locale: "sv_SE",
    url: "https://guidebanken.se",
    title: "Guidebanken - Din källa för praktiska guider och tips",
    description:
      "Guidebanken erbjuder praktiska guider och steg-för-steg instruktioner för att hjälpa dig lösa vardagliga problem och lära dig nya färdigheter.",
    siteName: "Guidebanken",
    images: [
      {
        url: "/images/guidebanken-logo.png",
        width: 512,
        height: 512,
        alt: "Guidebanken logotyp",
      },
    ],
  },
  twitter: {
    card: "summary_large_image",
    title: "Guidebanken - Din källa för praktiska guider och tips",
    description:
      "Guidebanken erbjuder praktiska guider och steg-för-steg instruktioner för att hjälpa dig lösa vardagliga problem och lära dig nya färdigheter.",
    images: ["/images/guidebanken-logo.png"],
  },
  robots: {
    index: true,
    follow: true,
  },
  generator: 'v0.dev'
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="sv">
      <head>
        <link rel="manifest" href="/manifest.json" />
      </head>
      <body className={`${montserrat.variable} ${merriweather.variable} font-sans`}>
        <div className="flex min-h-screen flex-col">
          <Header />
          <main className="flex-1">{children}</main>
          <Footer />
          <CookieBanner />
          <Toaster />
        </div>
      </body>
    </html>
  )
}
