import type { Guide } from "./types"
import { getAllGuides, getGuidesByCategory, getAllCategories } from "./markdown"

// This will be populated at build time
let _guides: Guide[] = []
let _categories: string[] = []

// Function to get all guides
export async function getGuides(): Promise<Guide[]> {
  // If guides are already loaded, return them
  if (_guides.length > 0) {
    return _guides
  }

  // Otherwise, load guides from markdown files
  _guides = await getAllGuides()
  return _guides
}

// Function to get guides by category
export async function getGuidesByCategory(category: string): Promise<Guide[]> {
  // Make sure guides are loaded
  if (_guides.length === 0) {
    await getGuides()
  }

  // Filter guides by category
  return _guides.filter(
    guide => guide.category.toLowerCase() === category.toLowerCase()
  )
}

// Function to get all categories
export async function getCategories(): Promise<string[]> {
  // If categories are already loaded, return them
  if (_categories.length > 0) {
    return _categories
  }

  // Otherwise, load categories
  _categories = await getAllCategories()
  return _categories
}

// For backwards compatibility, we'll export an empty array that will be populated later
export const guides: Guide[] = []
