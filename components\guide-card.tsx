import Link from "next/link"
import Image from "next/image"
import { ArrowRight } from "lucide-react"
import type { Guide } from "@/lib/types"
import { Button } from "@/components/ui/button"

export default function GuideCard({ guide }: { guide: Guide }) {
  return (
    <div className="group flex flex-col h-full overflow-hidden rounded-lg border bg-background shadow-sm transition-all hover:shadow-lg">
      <Link href={`/guider/${guide.slug}`} className="relative aspect-video overflow-hidden">
        <Image
          src={guide.image || "/placeholder.svg"}
          alt={guide.title}
          fill
          className="object-cover transition-transform group-hover:scale-105 duration-500"
        />
        <div className="absolute inset-0 bg-gradient-to-t from-black/50 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
      </Link>
      <div className="flex flex-col flex-1 p-6">
        <div className="flex-1">
          <Link
            href={`/kategorier/${guide.category.toLowerCase().replace(/\s+/g, '-')}`}
            className="inline-block rounded-full bg-primary/10 px-3 py-1 text-xs font-medium text-primary mb-3 hover:bg-primary/20 transition-colors"
          >
            {guide.category}
          </Link>
          <Link href={`/guider/${guide.slug}`}>
            <h3 className="text-xl font-bold mb-3 group-hover:text-primary transition-colors font-serif">{guide.title}</h3>
          </Link>
          <p className="text-muted-foreground mb-5 line-clamp-3">{guide.excerpt}</p>
        </div>
        <div className="flex items-center justify-between pt-3 border-t border-border">
          <div className="text-sm text-muted-foreground flex items-center">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
              <circle cx="12" cy="12" r="10"></circle>
              <polyline points="12 6 12 12 16 14"></polyline>
            </svg>
            Av {guide.author}
          </div>
          <Button asChild variant="ghost" size="sm" className="gap-1 hover:bg-primary/10">
            <Link href={`/guider/${guide.slug}`} className="flex items-center">
              Läs mer <ArrowRight className="h-4 w-4 ml-1" />
            </Link>
          </Button>
        </div>
      </div>
    </div>
  )
}
