import type { Metada<PERSON> } from "next"
import { getGuides } from "@/lib/guides-data"
import GuideCard from "@/components/guide-card"

export const metadata: Metadata = {
  title: "Guider - Steg-för-steg instruktioner för vardagliga problem",
  description:
    "Utforska vår samling av praktiska guider med steg-för-steg instruktioner för att lösa vardagliga problem och lära dig nya färdigheter.",
  keywords: ["guider", "hur gör man", "steg-för-steg", "instruktioner", "tips", "hjälp", "lösningar"],
}

export default async function GuidesPage() {
  // Fetch guides from markdown files
  const guides = await getGuides()

  return (
    <div className="container mx-auto px-4 md:px-6 py-12">
      <div className="max-w-3xl mx-auto text-center mb-12">
        <h1 className="text-4xl font-bold tracking-tight mb-4">Guider</h1>
        <p className="text-xl text-muted-foreground">
          Utforska vår samling av praktiska guider med steg-för-steg instruktioner för att lösa vardagliga problem och
          lära dig nya färdigheter.
        </p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {guides.map((guide) => (
          <GuideCard key={guide.slug} guide={guide} />
        ))}
      </div>
    </div>
  )
}
